import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardBody, Button } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import OverworldView from './OverworldView';
import GridView from './GridView';
import ContentRenderer from './ContentRenderer';
import NavigationContext from './NavigationContext';
import NavigationBreadcrumbs from './NavigationBreadcrumbs';
import EnhancedBreadcrumbs from './EnhancedBreadcrumbs';
import EnhancedMobileNavigation from './EnhancedMobileNavigation';
import AccessibleNavigation from './AccessibleNavigation';
import EnhancedNavigationProgress from './EnhancedNavigationProgress';
import SmartViewEnhancer from './SmartViewEnhancer';
import useCanvasDefinitions from '../../hooks/useCanvasDefinitions';
import { useNavigation } from '../../contexts/NavigationContext';
import OnboardingAuth from '../auth/OnboardingAuth';
import OnboardingFlow from '../onboarding/OnboardingFlow';
import OnboardingToProjectBridge from '../onboarding/OnboardingToProjectBridge';
import DebugConsole from '../debug/DebugConsole';
import GridTileManager from './GridTileManager';
import useKonamiCode from '../../hooks/useKonamiCode';
import activityLogger from '../../services/ActivityLogger';
import inputManager, { INPUT_MODES, setupKeyboardNavigation, setupTouchHandling, setupScrollHandling } from '../../utils/input-manager';

/**
 * Experimental Navigation System
 *
 * Features:
 * - Simplified dual view modes: Grid (dashboard) and Content (pages)
 * - Smooth zoom transitions between views
 * - Keyboard navigation support
 * - Bento grid layout for quick selection
 * - Clean, intuitive navigation experience
 */
const ExperimentalNavigation = ({ children, currentUser }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Use enhanced navigation context
  const {
    currentView,
    currentCanvas: contextCurrentCanvas,
    zoomLevel: contextZoomLevel,
    viewPosition: contextViewPosition,
    inputMode,
    allowScroll,
    isMobile,
    isTablet,
    preferences,
    actions
  } = useNavigation();

  // Navigation state - Enhanced with context integration
  const [viewMode, setViewMode] = useState(currentView || (() => {
    // Start in grid mode for main navigation, content mode for specific pages
    const path = window.location.pathname;
    const isSpecificPage = path !== '/' && path !== '/dashboard';
    return isSpecificPage ? 'content' : 'grid';
  }));
  const [currentCanvas, setCurrentCanvas] = useState(contextCurrentCanvas || 'home');
  const [isDragging, setIsDragging] = useState(false);
  const [viewPosition, setViewPosition] = useState(contextViewPosition || { x: 0, y: 0 }); // Persistent view position
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 }); // Temporary drag offset
  const [zoomLevel, setZoomLevel] = useState(contextZoomLevel || (() => {
    // Simplified zoom levels: 0.6 for grid, 1.0 for content
    const path = window.location.pathname;
    const isSpecificPage = path !== '/' && path !== '/dashboard';
    return isSpecificPage ? 1.0 : 0.6;
  }));
  const [isAdmin, setIsAdmin] = useState(false);
  const [presetView, setPresetView] = useState(null); // For preset view positions
  const [previousViewMode, setPreviousViewMode] = useState('grid'); // Track where user came from

  // Onboarding state
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isFirstTimeUser, setIsFirstTimeUser] = useState(false);
  const [onboardingStep, setOnboardingStep] = useState('auth'); // 'auth' | 'flow' | 'guided' | 'bridge' | 'complete'
  const [tutorialMode, setTutorialMode] = useState(false); // When true, shows overlay guidance
  const [tutorialStep, setTutorialStep] = useState('grid-intro'); // Current tutorial step in real interface
  const [showProjectBridge, setShowProjectBridge] = useState(false);

  // Debug console state with localStorage persistence
  const [showDebugConsole, setShowDebugConsole] = useState(() => {
    return localStorage.getItem('debug_console_open') === 'true';
  });
  const [showDebugUI, setShowDebugUI] = useState(() => {
    return localStorage.getItem('debug_ui_enabled') === 'true';
  });
  const [showCrosshair, setShowCrosshair] = useState(() => {
    return localStorage.getItem('debug_crosshair_enabled') === 'true';
  });
  const [debugPanelPosition, setDebugPanelPosition] = useState({ x: 0, y: 0 });
  const [isDraggingDebugPanel, setIsDraggingDebugPanel] = useState(false);
  const [debugPanelDragStart, setDebugPanelDragStart] = useState({ x: 0, y: 0 });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [mouseStatus, setMouseStatus] = useState('idle');
  const [lastPage, setLastPage] = useState('');
  const [selectedPage, setSelectedPage] = useState(''); // Only set when hovering over cards
  const [hoveredCard, setHoveredCard] = useState(''); // Track which card is being hovered

  // Smart View state
  const [smartViewActive, setSmartViewActive] = useState(false);
  const [selectedPath, setSelectedPath] = useState(null);

  // Layer transition state
  const [showContent, setShowContent] = useState(() => {
    // For test users, start with content visible
    const isTestMode = window.location.search.includes('test_mode=true') ||
                      window.location.search.includes('skip_onboarding=true');
    return isTestMode;
  });
  const [navigationOpacity, setNavigationOpacity] = useState(() => {
    // For test users, start with navigation hidden but allow it to be shown
    const isTestMode = window.location.search.includes('test_mode=true') ||
                      window.location.search.includes('skip_onboarding=true');
    return isTestMode ? 0 : 1;
  });
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Refs for drag handling
  const containerRef = useRef(null);
  const dragStartRef = useRef({ x: 0, y: 0 });
  const dragDistanceRef = useRef(0);

  // Initialize activity logger and fetch admin status
  useEffect(() => {
    const fetchAdminStatus = async () => {
      if (!currentUser) return;

      // Set user ID for activity logger
      activityLogger.setUserId(currentUser.id);

      try {
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (!error && data) {
          setIsAdmin(data.is_admin || false);
        }
      } catch (error) {
        console.error("Error fetching admin status:", error);
      }
    };

    fetchAdminStatus();
  }, [currentUser]);

  // Setup unified input manager - Split into separate effects to avoid infinite loops
  useEffect(() => {
    // Update input manager preferences
    inputManager.updatePreferences(preferences);
  }, [preferences]);

  useEffect(() => {
    // Set initial input mode based on view mode
    const initialMode = viewMode === 'content' ? INPUT_MODES.CONTENT : INPUT_MODES.NAVIGATION;
    inputManager.setMode(initialMode);
    actions.setInputMode(initialMode);
  }, [viewMode, actions]);

  useEffect(() => {
    // Setup input event listeners - only setup once
    const keyboardCleanup = setupKeyboardNavigation();
    const touchCleanup = setupTouchHandling(document.body);
    const scrollCleanup = setupScrollHandling(document.body);

    return () => {
      // Cleanup input handlers
      keyboardCleanup();
      touchCleanup();
      scrollCleanup();
    };
  }, []); // Empty dependency array - setup once

  useEffect(() => {
    // Listen for input manager events
    const handleModeChange = ({ current, previous }) => {
      actions.setInputMode(current);

      // Update scroll state based on mode
      if (current === INPUT_MODES.CONTENT) {
        actions.setScrollState({ allowScroll: true, scrollLocked: false });
      } else if (current === INPUT_MODES.NAVIGATION) {
        actions.setScrollState({ allowScroll: false, scrollLocked: true });
      }
    };

    const handleNavigate = ({ direction }) => {
      if (inputMode === INPUT_MODES.NAVIGATION) {
        handleArrowNavigation(direction);
      }
    };

    const handleEscape = () => {
      if (viewMode === 'content') {
        setLastPage(currentCanvas || 'content');
        showNavigationLayer();
        setViewMode('grid');
        setZoomLevel(0.6);
        setViewPosition({ x: 0, y: 0 });
        setDragOffset({ x: 0, y: 0 });
        navigate('/');
      }
    };

    const handleToggle = () => {
      if (!isTransitioning) {
        toggleViewMode();
      }
    };

    const handleReset = () => {
      if (viewMode !== 'content' && !isTransitioning) {
        resetView();
      }
    };

    const handleTouchDrag = ({ delta }) => {
      if (inputMode === INPUT_MODES.NAVIGATION && !isDragging) {
        setDragOffset(delta);
      }
    };

    const handleDragEnd = ({ delta }) => {
      if (inputMode === INPUT_MODES.NAVIGATION) {
        const newPosition = {
          x: viewPosition.x + delta.x,
          y: viewPosition.y + delta.y
        };
        setViewPosition(newPosition);
        actions.setPosition(newPosition);
        setDragOffset({ x: 0, y: 0 });
      }
    };

    // Register event listeners
    inputManager.on('modeChange', handleModeChange);
    inputManager.on('navigate', handleNavigate);
    inputManager.on('escape', handleEscape);
    inputManager.on('toggle', handleToggle);
    inputManager.on('reset', handleReset);
    inputManager.on('touchDrag', handleTouchDrag);
    inputManager.on('dragEnd', handleDragEnd);

    return () => {
      // Cleanup event listeners
      inputManager.off('modeChange', handleModeChange);
      inputManager.off('navigate', handleNavigate);
      inputManager.off('escape', handleEscape);
      inputManager.off('toggle', handleToggle);
      inputManager.off('reset', handleReset);
      inputManager.off('touchDrag', handleTouchDrag);
      inputManager.off('dragEnd', handleDragEnd);
    };
  }, [viewMode, inputMode, currentCanvas, isTransitioning, isDragging, navigate]); // Removed actions and viewPosition to prevent loops

  // Mouse tracking for debug UI and logging
  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Debug panel drag handlers
  const handleDebugPanelMouseDown = (e) => {
    if (e.target.closest('.debug-panel-header')) {
      setIsDraggingDebugPanel(true);
      setDebugPanelDragStart({
        x: e.clientX - debugPanelPosition.x,
        y: e.clientY - debugPanelPosition.y
      });
    }
  };

  const handleDebugPanelMouseMove = (e) => {
    if (isDraggingDebugPanel) {
      setDebugPanelPosition({
        x: e.clientX - debugPanelDragStart.x,
        y: e.clientY - debugPanelDragStart.y
      });
    }
  };

  const handleDebugPanelMouseUp = () => {
    setIsDraggingDebugPanel(false);
  };

  // Add global mouse event listeners for debug panel dragging
  useEffect(() => {
    if (isDraggingDebugPanel) {
      document.addEventListener('mousemove', handleDebugPanelMouseMove);
      document.addEventListener('mouseup', handleDebugPanelMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleDebugPanelMouseMove);
        document.removeEventListener('mouseup', handleDebugPanelMouseUp);
      };
    }
  }, [isDraggingDebugPanel, debugPanelDragStart]);

  // Get canvas definitions from hook
  const navigationCanvases = useCanvasDefinitions(currentUser, isAdmin);

  // Persist debug settings to localStorage
  useEffect(() => {
    localStorage.setItem('debug_console_open', showDebugConsole.toString());
  }, [showDebugConsole]);

  useEffect(() => {
    localStorage.setItem('debug_ui_enabled', showDebugUI.toString());
  }, [showDebugUI]);

  useEffect(() => {
    localStorage.setItem('debug_crosshair_enabled', showCrosshair.toString());
  }, [showCrosshair]);

  // Determine current canvas based on route
  useEffect(() => {
    const path = location.pathname;
    let canvas = 'home';

    // Map routes to canvases - ordered by specificity (most specific first)
    if (path.startsWith('/project/wizard')) canvas = 'wizard';
    else if (path.startsWith('/project') && path.includes('/tasks')) canvas = 'kanban';
    else if (path.startsWith('/project') && path.includes('/agreements')) canvas = 'agreements';
    else if (path.startsWith('/project') && path.includes('/royalty-calculator')) canvas = 'royalty';
    else if (path.startsWith('/project') && path.includes('/revenue')) canvas = 'escrow';
    else if (path.startsWith('/analytics/insights')) canvas = 'insights';
    else if (path.startsWith('/analytics/contributions')) canvas = 'analytics';
    else if (path.startsWith('/validation/metrics')) canvas = 'validation';
    else if (path.startsWith('/missions')) canvas = 'missions';
    else if (path.startsWith('/quests')) canvas = 'quests';
    else if (path.startsWith('/bounties')) canvas = 'bounties';
    else if (path.startsWith('/admin/system') && isAdmin) canvas = 'system';
    else if (path.startsWith('/start')) canvas = 'start';
    else if (path.startsWith('/projects')) canvas = 'projects';
    else if (path.startsWith('/contributions')) canvas = 'contributions';
    else if (path.startsWith('/track')) canvas = 'track';
    else if (path.startsWith('/earn')) canvas = 'earn';
    else if (path.startsWith('/revenue')) canvas = 'revenue';
    else if (path.startsWith('/learn')) canvas = 'learn';
    else if (path.startsWith('/profile')) canvas = 'profile';
    else if (path.startsWith('/social')) canvas = 'social';
    else if (path.startsWith('/teams') || path.startsWith('/alliances')) canvas = 'teams';
    else if (path.startsWith('/settings')) canvas = 'settings';
    else if (path.startsWith('/notifications')) canvas = 'notifications';
    else if (path.startsWith('/help')) canvas = 'help';
    else if (path.startsWith('/bugs')) canvas = 'bugs';
    else if (path.startsWith('/admin') && isAdmin) canvas = 'admin';
    else if (path === '/') canvas = 'home';

    // Track page changes for debug UI
    if (currentCanvas && currentCanvas !== canvas) {
      setLastPage(currentCanvas);
    }
    setCurrentCanvas(canvas);
    // Don't automatically set selectedPage - it should only be set when hovering
  }, [location.pathname, isAdmin, currentCanvas]);

  // Handle canvas navigation
  const navigateToCanvas = (canvasId, options = {}) => {
    const canvas = navigationCanvases[canvasId];
    if (canvas && canvas.route) {
      // Don't override lastPage if it was already set by handleCardSelection
      // Only set lastPage if we're navigating between different canvases in content mode
      if (viewMode === 'content' && currentCanvas && currentCanvas !== canvasId) {
        setLastPage(currentCanvas);
      }

      // Resolve route parameters if needed
      let resolvedRoute = canvas.route;

      // Check if route has parameters that need to be resolved
      if (resolvedRoute.includes(':id') || resolvedRoute.includes(':projectId')) {
        // For routes with parameters, we need to either:
        // 1. Use the current URL parameters if available
        // 2. Navigate to a default route instead
        // 3. Skip navigation if no parameters are available

        const currentPath = location.pathname;

        // Extract ID from current path if it matches a project route pattern
        const projectIdMatch = currentPath.match(/\/project\/([^\/]+)/);
        if (projectIdMatch && projectIdMatch[1]) {
          const projectId = projectIdMatch[1];
          resolvedRoute = resolvedRoute
            .replace(':id', projectId)
            .replace(':projectId', projectId);
        } else {
          // If no project ID is available, navigate to a safe default
          console.warn(`Cannot navigate to ${canvasId}: route requires parameters but none available`);
          if (canvasId === 'kanban' || canvasId === 'agreements') {
            resolvedRoute = '/projects'; // Navigate to projects list instead
          } else if (canvasId === 'royalty' || canvasId === 'escrow') {
            resolvedRoute = '/revenue'; // Navigate to revenue dashboard instead
          } else {
            return; // Skip navigation
          }
        }
      }

      // Log navigation event
      activityLogger.logNavigation(
        currentCanvas,
        canvasId,
        options.zoomToContent ? 'zoom_to_content' : 'normal_click',
        viewMode,
        {
          target_route: resolvedRoute,
          zoom_level: zoomLevel,
          view_position: viewPosition,
          last_page: lastPage,
          options: options
        }
      );

      setCurrentCanvas(canvasId);
      actions.setCanvas(canvasId);
      navigate(resolvedRoute);

      // If zooming into a card, show content directly
      if (options.zoomToContent) {
        // Remember where we came from before switching to content
        setPreviousViewMode(viewMode);
        showContentLayer();
        setViewMode('content');
        actions.setView('content');
        setZoomLevel(1.0);
        actions.setZoom(1.0);
      } else if (!showContent) {
        // Normal navigation - switch to content layer
        // Remember where we came from before switching to content
        setPreviousViewMode(viewMode);
        showContentLayer();
        setViewMode('content');
        actions.setView('content');
        setZoomLevel(1.0);
        actions.setZoom(1.0);
      }
    }
  };

  // Handle card selection with zoom behavior
  const handleCardSelection = (canvasId, fromZoom = false) => {
    // Always track that we're coming from grid when clicking cards
    setLastPage('grid');

    if (fromZoom) {
      // Zooming into a card - go directly to content
      navigateToCanvas(canvasId, { zoomToContent: true });
    } else {
      // Regular click - normal navigation
      navigateToCanvas(canvasId);
    }
  };

  // Handle card hover for debug UI
  const handleCardHover = (canvasId) => {
    setHoveredCard(canvasId);
    setSelectedPage(canvasId);

    // Log card hover interaction
    activityLogger.logCardInteraction(canvasId, 'hover_start', mousePosition, {
      view_mode: viewMode,
      zoom_level: zoomLevel
    });
  };

  // Handle card hover end for debug UI
  const handleCardHoverEnd = () => {
    const previousCard = hoveredCard;
    setHoveredCard('');
    setSelectedPage(''); // Clear selected page when mouse leaves card

    // Log card hover end
    if (previousCard) {
      activityLogger.logCardInteraction(previousCard, 'hover_end', mousePosition, {
        view_mode: viewMode,
        zoom_level: zoomLevel
      });
    }
  };



  // Handle layer transitions - Optimized for faster response
  const showNavigationLayer = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setShowContent(false);

    // Reduced timeout for faster navigation response
    setTimeout(() => {
      setNavigationOpacity(1);
      setIsTransitioning(false);
    }, 150); // Reduced from 300ms to 150ms
  };

  const showContentLayer = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    setNavigationOpacity(0);

    // Reduced timeout for faster navigation response
    setTimeout(() => {
      setShowContent(true);
      setIsTransitioning(false);
    }, 150); // Reduced from 300ms to 150ms
  };

  // Handle zoom toggle with layer transitions (simplified)
  const toggleViewMode = () => {
    const fromMode = viewMode;
    let toMode;

    if (viewMode === 'content') {
      // Content → Grid (simplified navigation)
      showNavigationLayer();
      toMode = 'grid';
      setViewMode('grid');
      setZoomLevel(1.0); // Full screen grid view
      setViewPosition({ x: 0, y: 0 });
      setDragOffset({ x: 0, y: 0 });
      // Navigate to home when going to grid
      navigate('/');
    } else {
      // Grid → Content (zoom into current canvas or home)
      toMode = 'content';
      const targetCanvas = currentCanvas || 'home';
      handleCardSelection(targetCanvas, true);
    }

    // Log view mode change
    if (toMode) {
      activityLogger.logViewModeChange(fromMode, toMode, 'toggle_function', {
        current_canvas: currentCanvas,
        zoom_level: zoomLevel,
        view_position: viewPosition
      });
    }
  };

  // Focus view on a specific canvas card
  const focusOnCanvas = (canvasId) => {
    if (!canvasId) return;

    // Calculate position to center on the specific canvas card
    const canvas = navigationCanvases[canvasId];
    if (canvas && canvas.position) {
      // Calculate the center position for the canvas card
      const centerX = window.innerWidth / 2;
      const centerY = window.innerHeight / 2;

      // Account for current zoom level when positioning
      const currentZoom = zoomLevel || 1.0;

      setViewPosition({
        x: centerX - (canvas.position.x * currentZoom),
        y: centerY - (canvas.position.y * currentZoom)
      });

      // Clear any drag offset
      setDragOffset({ x: 0, y: 0 });
    }
  };

  // Reset view to center with optimal zoom for current mode
  const resetView = () => {
    setViewPosition({ x: 0, y: 0 });
    setDragOffset({ x: 0, y: 0 });
    setZoomLevel(viewMode === 'grid' ? 1.0 : 1.0); // Grid shows full screen, content at normal zoom
  };

  // Set preset view for specific canvas
  const setPresetViewForCanvas = (canvasId) => {
    const canvas = navigationCanvases[canvasId];
    if (canvas && canvas.position) {
      setViewPosition({
        x: -canvas.position.x * 0.5,
        y: -canvas.position.y * 0.5
      });
      setZoomLevel(1.0);
      setViewMode('content');
    }
  };

  // Find the card closest to the center of the view
  const findCenterCard = () => {
    if (!containerRef.current) return null;

    const containerRect = containerRef.current.getBoundingClientRect();
    const centerX = containerRect.width / 2;
    const centerY = containerRect.height / 2;

    // Convert screen center to world coordinates
    const worldCenterX = (centerX - viewPosition.x - dragOffset.x) / zoomLevel;
    const worldCenterY = (centerY - viewPosition.y - dragOffset.y) / zoomLevel;

    let closestCard = null;
    let closestDistance = Infinity;

    Object.values(navigationCanvases).forEach(canvas => {
      const distance = Math.sqrt(
        Math.pow(canvas.position.x - worldCenterX, 2) +
        Math.pow(canvas.position.y - worldCenterY, 2)
      );

      if (distance < closestDistance) {
        closestDistance = distance;
        closestCard = canvas.id;
      }
    });

    // Only return a card if it's reasonably close to center (within 200px)
    return closestDistance < 200 ? closestCard : null;
  };

  // Handle arrow key navigation between view modes
  const handleArrowNavigation = (direction) => {
    if (isTransitioning) return;

    const fromMode = viewMode;
    let toMode = null;
    let targetCard = null;
    let success = false;

    if (direction === 'down') {
      // Arrow Down: content → grid (simplified)
      if (viewMode === 'content') {
        // Track that we're coming from content mode
        setLastPage(currentCanvas || 'content');
        showNavigationLayer();
        toMode = 'grid';
        setViewMode('grid');
        setZoomLevel(1.0); // Full screen grid view
        setViewPosition({ x: 0, y: 0 });
        setDragOffset({ x: 0, y: 0 });
        navigate('/'); // Go to home/grid
        success = true;
      }
      // Grid is the most zoomed out view, no further down
    } else if (direction === 'up') {
      // Arrow Up: grid → content (simplified)
      if (viewMode === 'grid') {
        // Track that we're coming from grid mode
        setLastPage('grid');
        // If there's a hovered card, navigate to it directly
        if (hoveredCard) {
          targetCard = hoveredCard;
          handleCardSelection(hoveredCard, true);
          toMode = 'content';
          success = true;
        } else {
          // Navigate to home/dashboard if no specific card is hovered
          targetCard = 'home';
          handleCardSelection('home', true);
          toMode = 'content';
          success = true;
        }
      }
      // Content is the most zoomed in view, no further up
    }

    // Log keyboard navigation
    activityLogger.logKeyboardNavigation(
      `Arrow${direction.charAt(0).toUpperCase() + direction.slice(1)}`,
      fromMode,
      toMode || fromMode,
      success,
      {
        target_card: targetCard,
        hovered_card: hoveredCard,
        selected_page: selectedPage,
        current_canvas: currentCanvas,
        last_page: lastPage
      }
    );
  };

  // Handle drag start
  const handleMouseDown = (e) => {
    setMouseStatus('down');

    if (viewMode === 'content') return; // Only allow dragging in navigation modes

    e.preventDefault();

    // Store initial drag info but don't start dragging immediately
    dragStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      timestamp: Date.now(),
      initialViewPosition: { ...viewPosition }
    };
    dragDistanceRef.current = 0;

    // Reset drag offset when starting new drag
    setDragOffset({ x: 0, y: 0 });

    // Log potential drag start
    activityLogger.logEvent('interaction', 'drag_interaction', 'drag_prepare', {
      element_type: 'background',
      mouse_position: { x: e.clientX, y: e.clientY },
      view_mode: viewMode,
      zoom_level: zoomLevel,
      view_position: viewPosition
    });
  };

  // Handle drag move
  const handleMouseMove = (e) => {
    if (!isDragging || viewMode === 'content' || !dragStartRef.current) return;

    const deltaX = e.clientX - dragStartRef.current.x;
    const deltaY = e.clientY - dragStartRef.current.y;

    // Track total drag distance
    dragDistanceRef.current = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Apply immediate visual feedback with smooth drag offset
    setDragOffset({ x: deltaX, y: deltaY });
  };

  // Handle drag end
  const handleMouseUp = (e) => {
    const wasDragging = isDragging && dragDistanceRef.current > 5; // 5px threshold
    const dragDuration = Date.now() - (dragStartRef.current?.timestamp || Date.now());

    setIsDragging(false);
    setMouseStatus('idle');

    if (wasDragging && dragStartRef.current) {
      // Log drag interaction
      activityLogger.logDragInteraction(
        dragStartRef.current,
        { x: e?.clientX || 0, y: e?.clientY || 0 },
        dragDuration,
        'background',
        {
          drag_distance: dragDistanceRef.current,
          view_mode: viewMode,
          zoom_level: zoomLevel,
          final_view_position: {
            x: viewPosition.x + dragOffset.x,
            y: viewPosition.y + dragOffset.y
          }
        }
      );

      // Commit the drag offset to the persistent view position
      const newPosition = {
        x: viewPosition.x + dragOffset.x,
        y: viewPosition.y + dragOffset.y
      };
      setViewPosition(newPosition);
      actions.setPosition(newPosition);
    }

    // Always reset drag offset after mouse up
    setDragOffset({ x: 0, y: 0 });
    dragDistanceRef.current = 0;
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Don't trigger shortcuts when typing in inputs
      if (e.target.tagName === 'INPUT' ||
          e.target.tagName === 'TEXTAREA' ||
          e.target.contentEditable === 'true') {
        return;
      }

      // Arrow key navigation between view modes
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        handleArrowNavigation('down');
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        handleArrowNavigation('up');
      }

      // ESC to zoom out from content to grid (simplified)
      if (e.key === 'Escape' && viewMode === 'content') {
        setLastPage(currentCanvas || 'content');
        showNavigationLayer();
        setViewMode('grid');
        setZoomLevel(1.0); // Full screen grid view
        setViewPosition({ x: 0, y: 0 });
        setDragOffset({ x: 0, y: 0 });
        navigate('/');
      }

      // Space or Tab to toggle navigation layer (simplified)
      if ((e.key === ' ' || e.key === 'Tab') && !isTransitioning) {
        e.preventDefault();
        toggleViewMode();
      }

      // G key is no longer needed in simplified navigation

      // R to reset view position
      if (e.key === 'r' && viewMode !== 'content' && !isTransitioning) {
        resetView();
      }

      // Ctrl+Shift+D to toggle debug console (alternative to Konami code)
      if (e.key === 'D' && e.ctrlKey && e.shiftKey) {
        e.preventDefault();
        const newConsoleState = !showDebugConsole;
        setShowDebugConsole(newConsoleState);
        console.log(`🎮 Debug console ${newConsoleState ? 'opened' : 'closed'} via Ctrl+Shift+D (persisted)`);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [viewMode, isTransitioning, currentCanvas, showDebugConsole, hoveredCard, selectedPage]);

  // Add event listeners
  useEffect(() => {
    // Global mouse move listener for debug tracking and drag handling
    const globalMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      handleMouseMove(e);
    };

    // Global mouse up listener for drag handling
    const globalMouseUp = (e) => {
      handleMouseUp(e);
    };

    // Touch event handlers for mobile support
    const globalTouchMove = (e) => {
      if (e.touches.length === 1 && isDragging) {
        e.preventDefault(); // Prevent scrolling
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
          clientX: touch.clientX,
          clientY: touch.clientY,
          bubbles: true
        });
        handleMouseMove(mouseEvent);
      }
    };

    const globalTouchEnd = (e) => {
      if (isDragging) {
        e.preventDefault();
        const mouseEvent = new MouseEvent('mouseup', {
          bubbles: true
        });
        handleMouseUp(mouseEvent);
      }
    };

    // Add all event listeners
    document.addEventListener('mousemove', globalMouseMove, { passive: false });
    document.addEventListener('mouseup', globalMouseUp);
    document.addEventListener('touchmove', globalTouchMove, { passive: false });
    document.addEventListener('touchend', globalTouchEnd, { passive: false });

    return () => {
      document.removeEventListener('mousemove', globalMouseMove);
      document.removeEventListener('mouseup', globalMouseUp);
      document.removeEventListener('touchmove', globalTouchMove);
      document.removeEventListener('touchend', globalTouchEnd);
    };
  }, [isDragging]); // Only depend on isDragging to avoid recreating listeners unnecessarily

  // Check if user is new (first time visiting)
  useEffect(() => {
    const checkFirstTimeUser = async () => {
      if (!currentUser) {
        setShowOnboarding(true);
        setOnboardingStep('auth');
        return;
      }

      // Check if user has completed onboarding
      const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${currentUser.id}`);

      // BYPASS ONBOARDING FOR TEST USERS AND DEVELOPMENT
      const isTestUser = currentUser.email?.includes('example.com') ||
                        currentUser.email?.includes('test') ||
                        window.location.search.includes('skip_onboarding=true') ||
                        process.env.NODE_ENV === 'development';

      if (!hasCompletedOnboarding && !isTestUser) {
        setIsFirstTimeUser(true);
        setShowOnboarding(true);
        setOnboardingStep('flow');
      } else if (isTestUser && !hasCompletedOnboarding) {
        // Auto-complete onboarding for test users
        console.log('🧪 Test user detected - bypassing onboarding');
        localStorage.setItem(`onboarding_completed_${currentUser.id}`, 'true');
        setIsFirstTimeUser(false);
        setShowOnboarding(false);
        setTutorialMode(false);
        setOnboardingStep('complete');
      }
    };

    checkFirstTimeUser();
  }, [currentUser]);

  // Handle authentication success
  const handleAuthSuccess = () => {
    setOnboardingStep('flow');
    setIsFirstTimeUser(true);
  };

  // Handle onboarding completion
  const handleOnboardingComplete = (selectedPath) => {
    console.log('Onboarding completed:', selectedPath);

    // Check if tutorial was skipped (no onboardingSelectedPath means skipped)
    const wasSkipped = !localStorage.getItem('onboardingSelectedPath');

    if (wasSkipped) {
      // Tutorial was skipped - go directly to normal completion
      setOnboardingStep('complete');
      setShowOnboarding(false);
      setTutorialMode(false);

      // Mark as completed and clean up
      if (currentUser) {
        localStorage.setItem(`onboarding_completed_${currentUser.id}`, 'true');
      }
      setIsFirstTimeUser(false);

      // Navigate to selected canvas in content mode
      if (selectedPath && selectedPath.canvas) {
        setCurrentCanvas(selectedPath.canvas);
      }
    } else {
      // Normal completion - transition to guided tutorial
      setOnboardingStep('guided');
      setShowOnboarding(false);
      setTutorialMode(true);
      setTutorialStep('grid-intro');

      // Set up the real navigation system for guided tutorial
      setViewMode('grid');
      setZoomLevel(1.0); // Full screen grid view
      showNavigationLayer();

      // Store selected path for Smart View weighting
      if (selectedPath && selectedPath.canvas) {
        setCurrentCanvas(selectedPath.canvas);
      }

      // Skip directly to smart-view step (no popup)
      setTutorialStep('smart-view');
    }
  };

  // Handle Smart View action (both tutorial and regular use)
  const handleSmartView = () => {
    console.log('Smart View activated');

    // Get the selected path from onboarding
    const storedPath = JSON.parse(localStorage.getItem('onboardingSelectedPath') || '{}');

    // Toggle Smart View
    const newSmartViewState = !smartViewActive;
    setSmartViewActive(newSmartViewState);

    if (newSmartViewState && storedPath.canvas) {
      setSelectedPath(storedPath);
      setCurrentCanvas(storedPath.canvas);
    } else {
      setSelectedPath(null);
    }

    // In tutorial mode, advance to next step
    if (tutorialMode && tutorialStep === 'smart-view') {
      setTutorialStep('final-zoom');
    }
  };

  // Memoized callback for SmartViewEnhancer to prevent infinite loops
  const handleLayoutChange = useCallback((layout) => {
    console.log('Smart layout:', layout);
  }, []);

  // Complete the guided tutorial
  const completeTutorial = () => {
    console.log('Tutorial completed');
    setTutorialMode(false);
    setOnboardingStep('bridge');
    setShowProjectBridge(true);

    // Mark user as having completed onboarding
    if (currentUser) {
      localStorage.setItem(`onboarding_completed_${currentUser.id}`, 'true');
    }
    // Don't remove onboardingSelectedPath yet - we need it for the project bridge
    setIsFirstTimeUser(false);
  };

  // Handle project bridge completion
  const handleProjectBridgeComplete = () => {
    setShowProjectBridge(false);
    setOnboardingStep('complete');
    localStorage.removeItem('onboardingSelectedPath');
  };

  // Debug console command handler
  const handleDebugCommand = (command, data) => {
    // Log debug command
    activityLogger.logDebugAction('debug_command', command, data, {
      view_mode: viewMode,
      current_canvas: currentCanvas,
      zoom_level: zoomLevel
    });

    switch (command) {
      case 'setViewMode':
        setViewMode(data);
        if (data === 'content') {
          showContentLayer();
          setZoomLevel(1.0);
        } else {
          showNavigationLayer();
          setZoomLevel(data === 'grid' ? 1.0 : 1.0); // Full screen for both modes
        }
        break;
      case 'resetView':
        resetView();
        break;
      case 'enableTutorial':
        setTutorialMode(true);
        setTutorialStep('grid-intro');
        setViewMode('grid');
        showNavigationLayer();
        break;
      case 'showDebugUI':
        const newDebugUI = !showDebugUI;
        setShowDebugUI(newDebugUI);
        console.log(`🐛 Debug UI ${newDebugUI ? 'enabled' : 'disabled'} (persisted)`);
        break;
      case 'toggleCrosshair':
        const newCrosshair = !showCrosshair;
        setShowCrosshair(newCrosshair);
        console.log(`🎯 Crosshair ${newCrosshair ? 'enabled' : 'disabled'} (persisted)`);
        break;
      case 'clearDebugCache':
        localStorage.removeItem('debug_console_open');
        localStorage.removeItem('debug_ui_enabled');
        localStorage.removeItem('debug_crosshair_enabled');
        setShowDebugConsole(false);
        setShowDebugUI(false);
        setShowCrosshair(false);
        console.log('🧹 Debug cache cleared');
        break;
      case 'exportLogs':
        activityLogger.exportSessionData().then(data => {
          console.log('📊 Session logs exported:', data);
          console.log('📋 Copy this data for analysis');
        });
        break;
      default:
        console.log('Unknown debug command:', command, data);
        console.log('Available commands: setViewMode, resetView, enableTutorial, showDebugUI, toggleCrosshair, clearDebugCache, exportLogs');
    }
  };

  // Konami code activation
  useKonamiCode(() => {
    setShowDebugConsole(true);
    console.log('🎮 Konami Code activated! Debug console opened (persisted).');
  });

  // Show onboarding if needed
  if (showOnboarding) {
    if (onboardingStep === 'auth') {
      return <OnboardingAuth onAuthSuccess={handleAuthSuccess} />;
    } else if (onboardingStep === 'flow') {
      return (
        <OnboardingFlow
          onComplete={handleOnboardingComplete}
          onNavigationAction={() => {}} // No longer needed since we transition to real interface
        />
      );
    }
  }

  // Show project bridge after onboarding
  if (showProjectBridge) {
    const storedPath = JSON.parse(localStorage.getItem('onboardingSelectedPath') || '{}');
    return (
      <OnboardingToProjectBridge
        selectedPath={storedPath}
        isVisible={showProjectBridge}
        onClose={handleProjectBridgeComplete}
        onCreateProject={handleProjectBridgeComplete}
      />
    );
  }

  // Show regular content for authenticated users who have completed onboarding
  if (!currentUser) {
    // In test mode, show a test user indicator instead of login
    const isTestMode = window.location.search.includes('test_mode=true');
    if (isTestMode) {
      return (
        <div className="fixed inset-0 w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          <Card className="bg-white/10 backdrop-blur-md border-white/20 max-w-md">
            <CardBody className="p-8 text-center">
              <span className="text-6xl mb-4 block">🧪</span>
              <h2 className="text-2xl font-bold text-white mb-4">Test Mode Active</h2>
              <p className="text-white/70 mb-6">
                You're in test mode, but you need to be authenticated to see the navigation system.
              </p>
              <div className="bg-white/5 rounded-lg p-4 text-left mb-6">
                <h3 className="text-white font-medium mb-2">To test the navigation:</h3>
                <div className="text-white/60 text-sm space-y-1">
                  <p>1. Sign in with any account</p>
                  <p>2. Add ?test_mode=true to the URL</p>
                  <p>3. Use ESC key to toggle canvas</p>
                  <p>4. Use 🗺️ button to show navigation</p>
                </div>
              </div>
              <Button
                onClick={() => window.location.href = '/login'}
                className="bg-blue-600 text-white"
              >
                Go to Login
              </Button>
            </CardBody>
          </Card>
        </div>
      );
    }
    return children;
  }

  return (
    <div className="fixed inset-0 w-full h-full overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Content Layer */}
      <div
        className={`absolute inset-0 z-10 ${allowScroll && inputMode === INPUT_MODES.CONTENT ? 'content-scrollable' : 'navigation-area'}`}
        style={{
          opacity: showContent ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          pointerEvents: showContent ? 'auto' : 'none',
          overflow: allowScroll && inputMode === INPUT_MODES.CONTENT ? 'auto' : 'hidden'
        }}
      >
        <ContentRenderer currentUser={currentUser} currentCanvas={currentCanvas} />
      </div>

      {/* Navigation Layer */}
      <div
        className="absolute inset-0 z-20"
        style={{
          opacity: navigationOpacity,
          transition: 'opacity 0.3s ease-in-out',
          pointerEvents: navigationOpacity > 0 ? 'auto' : 'none'
        }}
      >
        <div
          ref={containerRef}
          className="absolute inset-0"
          style={{
            transform: `scale(${zoomLevel}) translate(${viewPosition.x + dragOffset.x}px, ${viewPosition.y + dragOffset.y}px)`,
            WebkitTransform: `scale(${zoomLevel}) translate(${viewPosition.x + dragOffset.x}px, ${viewPosition.y + dragOffset.y}px)`,
            MozTransform: `scale(${zoomLevel}) translate(${viewPosition.x + dragOffset.x}px, ${viewPosition.y + dragOffset.y}px)`,
            transition: isDragging ? 'none' : 'transform 0.3s ease-out',
            WebkitTransition: isDragging ? 'none' : '-webkit-transform 0.3s ease-out',
            willChange: isDragging ? 'transform' : 'auto', // Optimize for dragging performance
            transformOrigin: 'center center'
          }}
        >
          {/* Content Layer - Simplified to Grid View only */}
          <div className="relative w-full h-full" style={{ zIndex: 2 }}>
            <AnimatePresence mode="wait">
              <GridView
                key="grid"
                canvases={navigationCanvases}
                currentCanvas={currentCanvas}
                onNavigate={handleCardSelection}
                onDragStart={handleMouseDown}
                onCardHover={handleCardHover}
                onCardHoverEnd={handleCardHoverEnd}
              />
            </AnimatePresence>

            {/* Smart View Enhancement Layer */}
            {viewMode === 'grid' && (
              <SmartViewEnhancer
                canvases={navigationCanvases}
                selectedPath={selectedPath}
                isActive={smartViewActive}
                onLayoutChange={handleLayoutChange}
              />
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Navigation */}
      {(isMobile || isTablet) && (
        <EnhancedMobileNavigation
          currentUser={currentUser}
          currentCanvas={currentCanvas}
          onCanvasChange={(canvasId) => {
            setCurrentCanvas(canvasId);
            actions.setCanvas(canvasId);
          }}
          onViewModeChange={(mode) => {
            setViewMode(mode);
            actions.setView(mode);
            if (mode === 'grid') {
              showNavigationLayer();
              setZoomLevel(0.6);
            } else {
              showContentLayer();
              setZoomLevel(1.0);
            }
          }}
          className="z-40"
        />
      )}

      {/* Enhanced Breadcrumbs - Show in content mode */}
      {viewMode === 'content' && !isMobile && (
        <div className="absolute top-4 left-4 z-50">
          <EnhancedBreadcrumbs
            currentCanvas={currentCanvas}
            onNavigate={(canvasId, options = {}) => {
              if (options.resolvedRoute) {
                // Use the resolved route from breadcrumb navigation
                navigate(options.resolvedRoute);
                setCurrentCanvas(canvasId);
                actions.setCanvas(canvasId);
                actions.navigateToCanvas(canvasId, { method: 'breadcrumb', resolvedRoute: options.resolvedRoute });
              } else {
                navigateToCanvas(canvasId);
                actions.navigateToCanvas(canvasId, { method: 'breadcrumb' });
              }
            }}
            showHistory={true}
            maxItems={4}
            className="bg-background/80 backdrop-blur-md rounded-lg px-3 py-2 border border-divider"
          />
        </div>
      )}

      {/* Top Right Button Group */}
      <div className="absolute top-4 right-4 z-50 flex gap-2">
        {/* Canvas Toggle Button - Show in test mode when in content view */}
        {viewMode === 'content' && (window.location.search.includes('test_mode=true') || window.location.search.includes('skip_onboarding=true')) && (
          <button
            onClick={() => {
              showNavigationLayer();
              const targetViewMode = previousViewMode || 'grid';
              setViewMode(targetViewMode);
              if (targetViewMode === 'grid') {
                setZoomLevel(1.0); // Full screen grid view
                setViewPosition({ x: 0, y: 0 });
                setDragOffset({ x: 0, y: 0 });
              } else {
                setZoomLevel(1.0);
                setTimeout(() => {
                  focusOnCanvas(currentCanvas);
                }, 100);
              }
            }}
            className="w-12 h-12 bg-blue-500/20 backdrop-blur-md rounded-full text-white hover:bg-blue-500/40 transition-colors flex items-center justify-center ring-2 ring-blue-400/50"
            style={{ backdropFilter: 'blur(12px)', WebkitBackdropFilter: 'blur(12px)' }}
            title="Show Navigation Canvas (ESC key)"
          >
            🗺️
          </button>
        )}

        {/* Back to Content Button - Show in navigation mode for test users */}
        {viewMode !== 'content' && (window.location.search.includes('test_mode=true') || window.location.search.includes('skip_onboarding=true')) && (
          <button
            onClick={() => {
              showContentLayer();
              setViewMode('content');
              setZoomLevel(1.0);
            }}
            className="w-12 h-12 bg-green-500/20 backdrop-blur-md rounded-full text-white hover:bg-green-500/40 transition-colors flex items-center justify-center ring-2 ring-green-400/50"
            style={{ backdropFilter: 'blur(12px)', WebkitBackdropFilter: 'blur(12px)' }}
            title="Back to Content (ESC key)"
          >
            📄
          </button>
        )}

        {/* Smart View Button - Only show in grid mode */}
        {viewMode === 'grid' && (
          <button
            onClick={handleSmartView}
            className={`w-12 h-12 backdrop-blur-md rounded-full text-white hover:bg-white/20 transition-colors flex items-center justify-center ${
              smartViewActive
                ? 'bg-green-500/30 ring-2 ring-green-400'
                : 'bg-white/10'
            } ${
              tutorialMode && tutorialStep === 'smart-view' ? 'ring-2 ring-yellow-400 animate-pulse' : ''
            }`}
            style={{ backdropFilter: 'blur(12px)', WebkitBackdropFilter: 'blur(12px)' }}
            title={smartViewActive ? "Smart View Active - Click to disable" : "Smart View - Organize around current selection"}
          >
            ✨
          </button>
        )}

        {/* Home Grid Button - Always visible for easy navigation back to grid */}
        <button
          onClick={() => {
            setLastPage(currentCanvas || 'content');
            showNavigationLayer();
            setViewMode('grid');
            setZoomLevel(1.0); // Full screen grid view
            setViewPosition({ x: 0, y: 0 });
            setDragOffset({ x: 0, y: 0 });
            navigate('/');
          }}
          className="w-12 h-12 bg-blue-500/20 backdrop-blur-md rounded-full text-white hover:bg-blue-500/40 transition-colors flex items-center justify-center ring-2 ring-blue-400/30"
          style={{ backdropFilter: 'blur(12px)', WebkitBackdropFilter: 'blur(12px)' }}
          title="Back to Home Grid (ESC key)"
        >
          🏠
        </button>
      </div>

      {/* Main Navigation Toggle - REMOVED per user request */}

      {/* Current Canvas Indicator - REMOVED per user request */}

      {/* Tutorial Overlay Guidance - Simplified */}
      {tutorialMode && (
        <div className="absolute inset-0 z-60 pointer-events-none">
          {tutorialStep === 'grid-intro' && (
            // Skip grid-intro step entirely - no popup
            <div style={{ display: 'none' }} />
          )}

          {tutorialStep === 'smart-view' && (
            <div className="absolute top-20 right-4 pointer-events-auto">
              <Card className="bg-yellow-100 backdrop-blur-md border-2 border-yellow-400">
                <CardBody className="p-4">
                  <h4 className="font-bold text-gray-800 mb-2">Try Smart View!</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Click the ✨ Smart View button to organize the grid around your selection.
                  </p>
                  <div className="text-xs text-gray-600">
                    ↗️ Look for the highlighted button above
                  </div>
                </CardBody>
              </Card>
            </div>
          )}

          {tutorialStep === 'final-zoom' && (
            <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 pointer-events-auto">
              <Card className="bg-green-100 backdrop-blur-md border-2 border-green-400">
                <CardBody className="p-4 text-center">
                  <h4 className="font-bold text-gray-800 mb-2">Perfect! Now navigate</h4>
                  <p className="text-gray-700 text-sm mb-3">
                    Use the ↑ arrow key to zoom into your chosen area, or ↓ to zoom out.
                  </p>
                  <Button
                    onClick={completeTutorial}
                    className="bg-green-600 text-white"
                    size="sm"
                  >
                    Skip to finish
                  </Button>
                </CardBody>
              </Card>
            </div>
          )}
        </div>
      )}



      {/* Debug UI Overlay */}
      {showDebugUI && (
        <div
          className="fixed z-[9998] bg-black bg-opacity-80 text-green-400 font-mono text-xs p-4 rounded border border-green-500 max-w-xs"
          style={{
            left: `${16 + debugPanelPosition.x}px`,
            top: `${16 + debugPanelPosition.y}px`,
            cursor: isDraggingDebugPanel ? 'grabbing' : 'default'
          }}
          onMouseDown={handleDebugPanelMouseDown}
        >
          <div className="debug-panel-header mb-2 text-green-300 font-bold cursor-grab active:cursor-grabbing select-none">🐛 Debug Info</div>
          <div className="space-y-1">
            <div><span className="text-gray-400">Current Page:</span> {viewMode === 'content' ? currentCanvas : viewMode}</div>
            <div><span className="text-gray-400">Last Page:</span> {lastPage || 'none'}</div>
            <div><span className="text-gray-400">Selected Page:</span> {selectedPage}</div>
            <div><span className="text-gray-400">Hovered Card:</span> {hoveredCard}</div>
            <div><span className="text-gray-400">View Mode:</span> {viewMode}</div>
            <div><span className="text-gray-400">Zoom Level:</span> {zoomLevel.toFixed(2)}</div>
            <div><span className="text-gray-400">Mouse:</span> ({mousePosition.x}, {mousePosition.y})</div>
            <div><span className="text-gray-400">Mouse Status:</span> {mouseStatus}</div>
            <div><span className="text-gray-400">Dragging:</span> {isDragging ? 'yes' : 'no'}</div>
            <div><span className="text-gray-400">View Position:</span> ({viewPosition.x.toFixed(0)}, {viewPosition.y.toFixed(0)})</div>
            <div><span className="text-gray-400">Drag Offset:</span> ({dragOffset.x.toFixed(0)}, {dragOffset.y.toFixed(0)})</div>
            <div><span className="text-gray-400">Crosshair:</span> {showCrosshair ? 'on' : 'off'}</div>
          </div>
        </div>
      )}

      {/* Crosshair Overlay */}
      {showCrosshair && (
        <div className="fixed inset-0 z-[9997] pointer-events-none">
          {/* Horizontal line */}
          <div
            className="absolute w-full h-px bg-red-500 opacity-70"
            style={{ top: mousePosition.y }}
          />
          {/* Vertical line */}
          <div
            className="absolute h-full w-px bg-red-500 opacity-70"
            style={{ left: mousePosition.x }}
          />
          {/* Center dot */}
          <div
            className="absolute w-2 h-2 bg-red-500 rounded-full opacity-90 transform -translate-x-1 -translate-y-1"
            style={{ left: mousePosition.x, top: mousePosition.y }}
          />
        </div>
      )}

      {/* Debug Console */}
      <DebugConsole
        isOpen={showDebugConsole}
        onClose={() => setShowDebugConsole(false)}
        onCommand={handleDebugCommand}
      />
    </div>
  );
};

export default ExperimentalNavigation;
